import React from "react";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
import { cn } from "@/lib/utils";

// Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";
import "./banner-swiper.css";

interface BannerItem {
  xid?: string;
  title: string;
  subtitle?: string;
  image_url: string;
  backgroundColor?: string;
  textColor?: string;
}

interface BannerCardProps {
  title: string;
  subtitle?: string;
  image: string;
  backgroundColor?: string;
  textColor?: string;
  className?: string;
  cardStyle?: any;
}

interface BannerSwiperProps {
  banners: BannerItem[];
  cardStyle?: any;
  autoplay?: boolean;
  autoplayDelay?: number;
  showPagination?: boolean;
  height?: string;
}

// Single Banner Card Component
export const BannerCard: React.FC<BannerCardProps> = ({
  title,
  subtitle,
  image,
  backgroundColor = "bg-gradient-to-r from-accent to-accent/80",
  textColor = "text-accent-foreground",
  className,
  cardStyle,
}) => {
  return (
    <div
      className={cn(
        "relative rounded-xl overflow-hidden h-24 flex items-center",
        backgroundColor,
        className
      )}
      style={{
        background: cardStyle?.background || "",
        border: cardStyle?.border || "",
        borderRadius: cardStyle?.borderRadius || "",
        padding: cardStyle?.padding || "",
        margin: cardStyle?.margin || "",
        boxShadow: cardStyle?.boxShadow || "",
      }}
    >
      <div className="flex-1 p-4 z-10">
        <h3 className={cn("font-bold text-lg leading-tight", textColor)}>
          {title}
        </h3>
        {subtitle && (
          <p className={cn("text-sm opacity-80", textColor)}>{subtitle}</p>
        )}
      </div>

      <div className="absolute right-0 top-0 h-full w-20 opacity-50">
        <img src={image} alt={title} className="w-full h-full object-cover" />
      </div>
    </div>
  );
};

// Banner Swiper Component
export const BannerSwiper: React.FC<BannerSwiperProps> = ({
  banners,
  cardStyle,
  autoplay = true,
  autoplayDelay = 3000,
  showPagination = true,
  height = "h-32",
}) => {
  if (!banners || banners.length === 0) {
    return (
      <div
        className={cn(
          "bg-gray-100 rounded-xl flex items-center justify-center",
          height
        )}
      >
        <p className="text-gray-500">Không có banner nào</p>
      </div>
    );
  }

  // If only one banner, show it without swiper
  if (banners.length === 1) {
    const banner = banners[0];
    return (
      <BannerCard
        title={banner.title}
        subtitle={banner.subtitle}
        image={banner.image_url}
        backgroundColor={banner.backgroundColor}
        textColor={banner.textColor}
        cardStyle={cardStyle}
      />
    );
  }

  return (
    <div className="relative">
      <Swiper
        modules={[Pagination, Autoplay]}
        spaceBetween={0}
        slidesPerView={1}
        pagination={
          showPagination
            ? {
                clickable: true,
                bulletClass: "swiper-pagination-bullet",
                bulletActiveClass: "swiper-pagination-bullet-active",
              }
            : false
        }
        autoplay={
          autoplay
            ? {
                delay: autoplayDelay,
                disableOnInteraction: false,
              }
            : false
        }
        loop={banners.length > 1}
        className="banner-swiper"
      >
        {banners.map((banner, index) => (
          <SwiperSlide key={banner.xid || index}>
            <div
              className={cn(
                "relative rounded-xl overflow-hidden flex items-center",
                height,
                banner.backgroundColor ||
                  "bg-gradient-to-r from-blue-500 to-purple-600"
              )}
              style={{
                background:
                  cardStyle?.background || banner.backgroundColor || "",
                border: cardStyle?.border || "",
                borderRadius: cardStyle?.borderRadius || "",
                padding: cardStyle?.padding || "",
                margin: cardStyle?.margin || "",
                boxShadow: cardStyle?.boxShadow || "",
              }}
            >
              <div className="flex-1 p-4 z-10">
                <h3
                  className={cn(
                    "font-bold text-lg leading-tight",
                    banner.textColor || "text-white"
                  )}
                >
                  {banner.title}
                </h3>
                {banner.subtitle && (
                  <p
                    className={cn(
                      "text-sm opacity-80 mt-1",
                      banner.textColor || "text-white"
                    )}
                  >
                    {banner.subtitle}
                  </p>
                )}
              </div>

              {banner.image_url && (
                <div className="absolute right-0 top-0 h-full w-24 opacity-70">
                  <img
                    src={banner.image_url}
                    alt={banner.title}
                    className="w-full h-full object-cover"
                  />
                </div>
              )}
            </div>
          </SwiperSlide>
        ))}
      </Swiper>

      {/* Custom pagination styles are handled via CSS classes */}
    </div>
  );
};
